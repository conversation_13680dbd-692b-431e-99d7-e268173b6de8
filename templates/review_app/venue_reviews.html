{% extends 'review_app/base_review.html' %}
{% load i18n %}

{% block title %}Reviews - {{ venue.venue_name }}{% endblock %}

{% block extra_css %}
{% load static %}
<style>
.review-card {
    transition: transform 0.2s;
}
.review-card:hover {
    transform: translateY(-2px);
}
.skeleton-loader {
    background-color: #eee;
    border-radius: 4px;
    animation: pulse 1.5s infinite;
}
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 50;
}
</style>
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
            <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue.slug %}">{{ venue.venue_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Reviews</li>
        </ol>
    </nav>

    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            {% if venue.main_image %}
                                <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" 
                                     class="img-fluid rounded">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 100px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-7">
                            <h2 class="mb-2">{{ venue.venue_name }}</h2>
                            <p class="text-muted mb-2">{{ venue.service_provider.business_name }}</p>
                            <p class="mb-0">{{ venue.short_description|truncatewords:25 }}</p>
                        </div>
                        <div class="col-md-3 text-center">
                            {% if can_review and not has_reviewed %}
                                <a href="{% url 'review_app:submit_review' venue.id %}" 
                                   class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-star me-2"></i>Write a Review
                                </a>
                            {% elif has_reviewed %}
                                <div class="alert alert-info mb-2">
                                    <i class="fas fa-check-circle me-2"></i>You've already reviewed this venue
                                </div>
                            {% endif %}
                            <a href="{% url 'venues_app:venue_detail' venue.slug %}" 
                               class="btn btn-outline-secondary w-100">
                                <i class="fas fa-arrow-left me-2"></i>Back to Venue
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Summary -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <h1 class="display-4 text-warning mb-2">{{ average_rating|floatformat:1 }}</h1>
                    <div class="text-warning mb-2">
                        {% for i in "12345" %}
                            {% if forloop.counter <= average_rating %}
                                <i class="fas fa-star fa-lg"></i>
                            {% else %}
                                <i class="far fa-star fa-lg"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <p class="text-muted mb-0">Based on {{ total_reviews }} review{{ total_reviews|pluralize }}</p>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h6 class="mb-3">Rating Breakdown</h6>
                    <canvas id="ratingChart" height="160" aria-label="Rating distribution chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div id="reviews-skeleton">
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
        <div class="card skeleton-loader mb-3" style="height:120px;"></div>
    </div>

    {% if reviews %}
        <!-- Reviews List -->
        <div class="row align-items-end mb-3">
            <div class="col-md-8">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Customer Reviews
                    <span class="badge bg-primary ms-2">{{ total_reviews }}</span>
                </h4>
            </div>
            <div class="col-md-4 text-end">
                <form method="get" id="sortForm">
                    <div class="input-group">
                        <label class="input-group-text" for="sortSelect">Sort by</label>
                        <select name="sort" id="sortSelect" class="form-select" onchange="document.getElementById('sortForm').submit();">
                            <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest</option>
                            <option value="oldest" {% if current_sort == 'oldest' %}selected{% endif %}>Oldest</option>
                            <option value="rating_desc" {% if current_sort == 'rating_desc' %}selected{% endif %}>Highest Rating</option>
                            <option value="rating_asc" {% if current_sort == 'rating_asc' %}selected{% endif %}>Lowest Rating</option>
                        </select>
                    </div>
                    {% if reviews.number > 1 %}
                        <input type="hidden" name="page" value="{{ reviews.number }}">
                    {% endif %}
                </form>
            </div>
        </div>

        <div class="row">
            {% for review in reviews %}
            <div class="col-12 mb-4">
                <div class="card review-card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <!-- Customer Avatar -->
                            <div class="me-3">
                                {% if review.customer.customerprofile.profile_picture %}
                                    <img src="{{ review.customer.customerprofile.profile_picture.url }}" 
                                         alt="Customer" class="rounded-circle" style="width: 60px; height: 60px;">
                                {% else %}
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-muted"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Review Content -->
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">{{ review.customer.customerprofile.get_full_name|default:"Anonymous Customer" }}</h6>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ review.created_at|date:"F d, Y" }}</small>
                                        {% if user.is_authenticated and user != review.customer %}
                                            <br>
                                            <a href="{% url 'review_app:flag_review' review.slug %}"
                                               class="btn btn-sm btn-outline-warning mt-1">
                                                <i class="fas fa-flag me-1"></i>Flag
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <p class="mb-3">{{ review.written_review }}</p>
                                
                                <!-- Provider Response -->
                                {% if review.response %}
                                <div class="card bg-light">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-start">
                                            <div class="me-3">
                                                {% if venue.service_provider.business_logo %}
                                                    <img src="{{ venue.service_provider.business_logo.url }}" 
                                                         alt="Business" class="rounded" style="width: 40px; height: 40px;">
                                                {% else %}
                                                    <div class="rounded bg-primary d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-store text-white"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <button class="btn btn-link p-0" data-bs-toggle="collapse" data-bs-target="#resp-{{ review.id }}" aria-expanded="false" aria-controls="resp-{{ review.id }}">
                                                    <i class="fas fa-reply me-2"></i>{% trans 'View Provider Response' %}
                                                </button>
                                                <div id="resp-{{ review.id }}" class="collapse mt-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-reply me-2"></i>Response from {{ venue.service_provider.business_name }}
                                                        </h6>
                                                        <small class="text-muted">{{ review.response.created_at|date:"F d, Y" }}</small>
                                                    </div>
                                                    <p class="mb-0">{{ review.response.response_text }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if reviews.has_other_pages %}
        <nav aria-label="Reviews pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reviews.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.previous_page_number }}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.next_page_number }}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ reviews.paginator.num_pages }}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card text-center p-4">
                    <h5 class="mb-2">No Reviews Yet</h5>
                    <p class="text-muted mb-3">Be the first to share your experience!</p>
                    {% if can_review %}
                        <a href="{% url 'review_app:submit_review' venue.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-star me-1"></i>Write Review
                        </a>
                    {% else %}
                        <p class="text-muted small">You need to complete a booking to leave a review.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Review Guidelines -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="accordion" id="guidelinesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingGuide">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGuide" aria-expanded="false" aria-controls="collapseGuide">
                            <i class="fas fa-info-circle me-2"></i>Review Guidelines
                        </button>
                    </h2>
                    <div id="collapseGuide" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="small fw-bold text-success">What makes a great review:</h6>
                                    <ul class="small text-muted">
                                        <li>Share your honest experience</li>
                                        <li>Be specific about services received</li>
                                        <li>Mention staff and atmosphere</li>
                                        <li>Include helpful details for other customers</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="small fw-bold text-warning">Please avoid:</h6>
                                    <ul class="small text-muted">
                                        <li>Offensive or inappropriate language</li>
                                        <li>Personal information about staff</li>
                                        <li>Unrelated or promotional content</li>
                                        <li>Reviews about other venues</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<button id="backToTop" class="btn btn-primary back-to-top" aria-label="Back to top">
    <i class="fas fa-arrow-up"></i>
</button>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{{ rating_distribution|json_script:"rating-data" }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('reviews-skeleton').style.display = 'none';
    var btn = document.getElementById('backToTop');
    window.addEventListener('scroll', function(){
        if (window.pageYOffset > 300) { btn.style.display = 'block'; } else { btn.style.display = 'none'; }
    });
    btn.addEventListener('click', function(){
        window.scrollTo({top:0, behavior:'smooth'});
    });
    const data = JSON.parse(document.getElementById('rating-data').textContent);
    const labels = Object.keys(data).map(r => r + '★').reverse();
    const counts = Object.keys(data).map(r => data[r]).reverse();
    new Chart(document.getElementById('ratingChart').getContext('2d'), {
        type: 'bar',
        data: {labels: labels, datasets:[{data: counts, backgroundColor:'#ffc107'}]},
        options: {scales:{y:{beginAtZero:true, ticks:{precision:0}}}}
    });
});
</script>
{% endblock %}
