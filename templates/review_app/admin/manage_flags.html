{% extends 'review_app/base_review.html' %}

{% block title %}Manage Review Flags - Admin Panel{% endblock %}

{% block extra_css %}
{% load static %}
{% endblock %}

{% block review_content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-flag me-2 text-warning"></i>Manage Review Flags
                </h2>
                <div>
                    <a href="{% url 'review_app:admin_review_moderation' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-shield-alt me-2"></i>All Reviews
                    </a>
                    <span class="badge bg-warning fs-6">{{ flags.paginator.count }} Flag{{ flags.paginator.count|pluralize }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning bg-opacity-10">
                <div class="card-body text-center">
                    <h3 class="text-warning">{{ flag_stats.pending_flags }}</h3>
                    <p class="mb-0">Pending Flags</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info bg-opacity-10">
                <div class="card-body text-center">
                    <h3 class="text-info">{{ flag_stats.reviewed_flags }}</h3>
                    <p class="mb-0">Reviewed Flags</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success bg-opacity-10">
                <div class="card-body text-center">
                    <h3 class="text-success">{{ flag_stats.resolved_flags }}</h3>
                    <p class="mb-0">Resolved Flags</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary bg-opacity-10">
                <div class="card-body text-center">
                    <h3 class="text-secondary">{{ flag_stats.total_flags }}</h3>
                    <p class="mb-0">Total Flags</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {% if status_filter == "all" %}selected{% endif %}>All Flags</option>
                                <option value="pending" {% if status_filter == "pending" %}selected{% endif %}>Pending</option>
                                <option value="reviewed" {% if status_filter == "reviewed" %}selected{% endif %}>Reviewed</option>
                                <option value="resolved" {% if status_filter == "resolved" %}selected{% endif %}>Resolved</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="reason" class="form-label">Reason</label>
                            <select name="reason" id="reason" class="form-select">
                                <option value="">All Reasons</option>
                                {% for value, label in reason_choices %}
                                    <option value="{{ value }}" {% if reason_filter == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control"
                                   placeholder="Search flags..." value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if flags %}
        <!-- Flags List -->
        <div class="row">
            {% for flag in flags %}
            <div class="col-12 mb-4">
                <div class="card shadow-sm border-warning">
                    <div class="card-body">
                        <div class="row">
                            <!-- Flag Information -->
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <i class="fas fa-flag fa-2x text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Flag #{{ flag.id }}</h6>
                                        <span class="badge bg-{% if flag.status == 'pending' %}warning{% elif flag.status == 'reviewed' %}info{% else %}success{% endif %}">
                                            {{ flag.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <strong>Flagged by:</strong><br>
                                        {{ flag.flagged_by.email }}
                                    </small>
                                </div>
                                
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ flag.created_at|date:"M d, Y g:i A" }}
                                </small>
                                
                                {% if flag.reviewed_by %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>Reviewed by:</strong><br>
                                        {{ flag.reviewed_by.email }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- Review Content -->
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">Flagged Review</h6>
                                            <div class="text-warning">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= flag.review.rating %}
                                                        <i class="fas fa-star"></i>
                                                    {% else %}
                                                        <i class="far fa-star"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        
                                        <p class="mb-2">{{ flag.review.written_review|truncatewords:30 }}</p>
                                        
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <strong>Customer:</strong> {{ flag.review.customer.email }}
                                            </small>
                                            <small class="text-muted">
                                                {{ flag.review.created_at|date:"M d, Y" }}
                                            </small>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <strong>Venue:</strong> {{ flag.review.venue.venue_name }}
                                            </small>
                                            <div>
                                                {% if flag.review.is_approved %}
                                                    <span class="badge bg-success">Approved</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Not Approved</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Flag Details -->
                                <div class="mt-3">
                                    <h6 class="small fw-bold">Flag Reason:</h6>
                                    <p class="small text-muted mb-1">{{ flag.get_reason_display }}</p>
                                    {% if flag.reason_text %}
                                        <p class="small text-muted mb-0"><em>"{{ flag.reason_text }}"</em></p>
                                    {% endif %}
                                </div>
                                
                                {% if flag.admin_notes %}
                                <div class="mt-3">
                                    <h6 class="small fw-bold">Admin Notes:</h6>
                                    <p class="small text-muted mb-0">{{ flag.admin_notes }}</p>
                                </div>
                                {% endif %}
                            </div>
                            
                            <!-- Actions -->
                            <div class="col-md-3">
                                <div class="d-grid gap-2">
                                    <a href="{% url 'review_app:admin_moderate_review' flag.review.slug %}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>Moderate Review
                                    </a>
                                    
                                    {% if flag.status == 'pending' %}
                                        <a href="{% url 'review_app:admin_resolve_flag' flag.id %}" 
                                           class="btn btn-warning btn-sm">
                                            <i class="fas fa-gavel me-1"></i>Resolve Flag
                                        </a>
                                    {% else %}
                                        <div class="text-center">
                                            <span class="badge bg-{% if flag.status == 'reviewed' %}info{% else %}success{% endif %} fs-6">
                                                {{ flag.get_status_display }}
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if flags.has_other_pages %}
        <nav aria-label="Flags pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if flags.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if reason_filter %}&reason={{ reason_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flags.previous_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if reason_filter %}&reason={{ reason_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in flags.paginator.page_range %}
                    {% if flags.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > flags.number|add:'-3' and num < flags.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if reason_filter %}&reason={{ reason_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if flags.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flags.next_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if reason_filter %}&reason={{ reason_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flags.paginator.num_pages }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-flag fa-4x text-muted mb-3"></i>
                        <h4>No Flagged Reviews</h4>
                        <p class="text-muted mb-4">
                            {% if status_filter != 'all' or request.GET.search %}
                                No flagged reviews match your current filters. Try adjusting your search criteria.
                            {% else %}
                                No reviews have been flagged yet. This is a good sign!
                            {% endif %}
                        </p>
                        {% if status_filter != 'all' or request.GET.search %}
                            <a href="{% url 'review_app:admin_manage_flags' %}" class="btn btn-primary">
                                <i class="fas fa-refresh me-2"></i>Clear Filters
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
